import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  StatusBar,
  ScrollView,
  Alert,
  Platform,
  Keyboard,
  Modal,
  Linking,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import mobileAds from 'react-native-google-mobile-ads';
import {openInbox} from 'react-native-email-link';
import Mailer from 'react-native-mail';
import {
  addRecentRecipient,
  getRecentRecipients,
  clearRecentRecipients,
  RecentRecipient,
  getEmailTemplates,
  EmailTemplate,
  addEmailTemplate,
  deleteEmailTemplate,
  RecentAttachment,
  getRecentAttachments,
  addRecentAttachment,
  clearRecentAttachments,
} from './src/utils/storage';
import RecentRecipients from './src/components/RecentRecipients';
import SaveTemplateModal from './src/components/SaveTemplateModal';
import AttachmentManager from './src/components/AttachmentManager';
import {pickDocument, Attachment} from './src/utils/attachments';
import {AdComponent} from './src/components/AdComponent';
import TemplateBar from './src/components/TemplateBar';
import RichTextEditor, {
  RichTextEditorRef,
} from './src/components/RichTextEditor';
import AboutUsPage from './src/pages/AboutUs';

export default function App() {
  const [to, setTo] = useState('');
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [editorReady, setEditorReady] = useState(false);
  const [recentRecipients, setRecentRecipients] = useState<RecentRecipient[]>(
    [],
  );
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [saveTemplateModalVisible, setSaveTemplateModalVisible] =
    useState(false);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [recentAttachments, setRecentAttachments] = useState<
    RecentAttachment[]
  >([]);
  const [recipientDropdownVisible, setRecipientDropdownVisible] =
    useState(false);
  const [recipientInputPosition, setRecipientInputPosition] = useState<
    {top: number; left: number; width: number} | undefined
  >();
  const [menuVisible, setMenuVisible] = useState(false);
  const [showAboutUs, setShowAboutUs] = useState(false);

  // Refs for measuring positions
  const recipientInputRef = useRef<TextInput>(null);
  const richTextEditorRef = useRef<RichTextEditorRef>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  // Load data when component mounts
  useEffect(() => {
    loadRecentRecipients();
    loadEmailTemplates();
    loadRecentAttachments();

    // Initialize the Google Mobile Ads SDK
    mobileAds()
      .initialize()
      .then(adapterStatuses => {
        // Initialization complete
        console.log('Initialization complete:', adapterStatuses);
      })
      .catch(error => {
        console.error('Initialization error:', error);
      });

    // Set editor ready after a delay to ensure it's fully mounted
    const timer = setTimeout(() => {
      setEditorReady(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const loadRecentRecipients = async () => {
    try {
      const recipients = await getRecentRecipients();
      setRecentRecipients(recipients);
    } catch (error) {
      console.error('Error loading recent recipients:', error);
    }
  };

  const loadEmailTemplates = async () => {
    try {
      const templates = await getEmailTemplates();
      setEmailTemplates(templates);
    } catch (error) {
      console.error('Error loading email templates:', error);
    }
  };

  const loadRecentAttachments = async () => {
    try {
      const attachmentsList = await getRecentAttachments();
      setRecentAttachments(attachmentsList);
    } catch (error) {
      console.error('Error loading recent attachments:', error);
    }
  };

  const handleSend = async () => {
    // Save recipient to recent list if valid
    if (to && to.includes('@')) {
      await addRecentRecipient(to);
      // Refresh the list
      loadRecentRecipients();
    }

    // Get the HTML content from the rich text editor
    let emailBody = body;
    if (richTextEditorRef.current && editorReady) {
      try {
        const html = await richTextEditorRef.current.getContentHTML();
        if (html && typeof html === 'string') {
          emailBody = html;
        }
      } catch (err) {
        console.error('Error getting rich text content:', err);
      }
    }

    // Save attachments to recent attachments
    for (const attachment of attachments) {
      if (attachment && attachment.uri) {
        await addRecentAttachment({
          uri: attachment.uri,
          type: attachment.type || '',
          name: attachment.name || '',
          size: attachment.size || 0,
        });
      }
    }

    // Refresh recent attachments list if there were any attachments
    if (attachments.length > 0) {
      loadRecentAttachments();
    }

    try {
      // Format attachments safely
      const formattedAttachments = attachments
        .filter(attachment => attachment && attachment.uri)
        .map(attachment => ({
          uri: attachment.uri,
          type: attachment.type || '',
          name: attachment.name || '',
        }));

      console.log('Sending email with attachments:', formattedAttachments);

      // Opens the native composer with fields pre-filled and attachments
      Mailer.mail(
        {
          subject: subject || '',
          recipients: to ? [to] : [],
          body: emailBody || '',
          isHTML: true, // Set to true for rich text
          attachments:
            formattedAttachments.length > 0 ? formattedAttachments : undefined,
        },
        error => {
          if (error) {
            Alert.alert(
              'Error',
              'Could not send mail. Please try again later.',
            );
            console.error('Error sending mail:', error);
          }
        },
      );
    } catch (error) {
      console.error('Error preparing email:', error);
      Alert.alert('Error', 'Failed to prepare email. Please try again.');
    }
  };

  const handleOpenApp = () => {
    // Opens the user's default mail app inbox
    try {
      openInbox();
    } catch (error) {
      console.error('Error opening mail app:', error);
      Alert.alert('Error', 'Could not open mail app.');
    }
  };

  const handleSelectRecipient = useCallback((email: string) => {
    console.log('Selected recipient:', email);

    // Empty string is a signal to close the dropdown (from the close button)
    if (email === '') {
      setRecipientDropdownVisible(false);
      return;
    }

    // Set the email in the input field
    setTo(email);
    setTimeout(() => {
      setRecipientDropdownVisible(false);
    }, 200);
  }, []);

  const handleClearRecipients = useCallback(async () => {
    console.log('Clearing recipients');
    try {
      await clearRecentRecipients();
      setRecentRecipients([]);
    } catch (error) {
      console.error('Error clearing recipients:', error);
    }

    // After clearing, there are no recipients to show, so close the dropdown
    setRecipientDropdownVisible(false);
  }, []);

  const handleSelectTemplate = useCallback(
    (template: EmailTemplate) => {
      if (!template) return;

      setSubject(template.subject || '');
      setBody(template.body || ''); // Keep this for backward compatibility

      // Give the editor a moment to initialize if it hasn't already
      if (richTextEditorRef.current && editorReady) {
        setTimeout(() => {
          if (richTextEditorRef.current) {
            richTextEditorRef.current.setContentHTML(template.body || '');
          }
        }, 100);
      }
    },
    [editorReady],
  );

  // Update the handleSaveTemplate function to use the rich text editor's content
  const handleSaveTemplate = useCallback(async (template: EmailTemplate) => {
    if (!template) return;

    try {
      // The template.body should already have the HTML content from the SaveTemplateModal
      await addEmailTemplate(template);
      // Refresh templates list
      loadEmailTemplates();
      setSaveTemplateModalVisible(false);
    } catch (error) {
      console.error('Error saving template:', error);
      Alert.alert('Error', 'Failed to save template. Please try again.');
    }
  }, []);

  const handleRemoveTemplate = useCallback(async (templateId: string) => {
    if (!templateId) return;

    try {
      await deleteEmailTemplate(templateId);
      // Refresh templates list
      loadEmailTemplates();
    } catch (error) {
      console.error('Error removing template:', error);
    }
  }, []);

  const handleOpenSaveTemplateModal = useCallback(() => {
    setSaveTemplateModalVisible(true);
  }, []);

  const handleAddAttachment = useCallback(async () => {
    try {
      const attachment = await pickDocument();
      if (attachment) {
        console.log('Adding attachment:', attachment);
        setAttachments(prev => [...prev, attachment]);

        // Save to recent attachments
        await addRecentAttachment({
          uri: attachment.uri,
          type: attachment.type || '',
          name: attachment.name || '',
          size: attachment.size || 0,
        });

        // Refresh recent attachments list
        loadRecentAttachments();
      }
    } catch (error) {
      console.error('Error adding attachment:', error);
      Alert.alert('Error', 'Failed to add attachment. Please try again.');
    }
  }, []);

  const handleSelectRecentAttachment = useCallback(
    (attachment: RecentAttachment) => {
      if (!attachment || !attachment.uri) return;

      // Convert RecentAttachment to Attachment
      const newAttachment: Attachment = {
        uri: attachment.uri,
        type: attachment.type || '',
        name: attachment.name || '',
        size: attachment.size || 0,
      };

      // Add to current attachments
      setAttachments(prev => [...prev, newAttachment]);
    },
    [],
  );

  const handleClearRecentAttachments = useCallback(async () => {
    try {
      await clearRecentAttachments();
      setRecentAttachments([]);
    } catch (error) {
      console.error('Error clearing recent attachments:', error);
    }
  }, []);

  const handleRemoveAttachment = useCallback(
    (index: number) => {
      if (typeof index !== 'number' || index < 0 || index >= attachments.length)
        return;

      setAttachments(prev => {
        const newAttachments = [...prev];
        newAttachments.splice(index, 1);
        return newAttachments;
      });
    },
    [attachments.length],
  );

  // Function to measure recipient input position for dropdown placement
  const measureRecipientInput = useCallback(() => {
    if (recipientInputRef.current) {
      recipientInputRef.current.measureInWindow((x, y, width, height) => {
        setRecipientInputPosition({
          top: y + height,
          left: x,
          width: width,
        });
      });
    }
  }, []);

  // Handle cursor position for rich text editor to scroll properly
  const handleCursorPosition = useCallback((scrollY: number) => {
    if (scrollViewRef.current && typeof scrollY === 'number') {
      scrollViewRef.current.scrollTo({
        y: scrollY - 30,
        animated: true,
      });
    }
  }, []);

  // Measure input position when component mounts
  useEffect(() => {
    // Small delay to ensure component is fully rendered
    const timer = setTimeout(() => {
      measureRecipientInput();
    }, 500);

    return () => clearTimeout(timer);
  }, [measureRecipientInput]);

  // Handle recipient input focus
  const handleRecipientFocus = useCallback(() => {
    if (recentRecipients.length > 0) {
      measureRecipientInput();
      setRecipientDropdownVisible(true);
    }
  }, [recentRecipients.length, measureRecipientInput]);

  // Handle recipient input blur
  const handleRecipientBlur = useCallback(() => {
    // Much longer delay to allow for selection from dropdown
    setTimeout(() => {
      // Don't close the dropdown if we're in the middle of selecting a recipient
      // The recipient handlers will close it when they're done
      console.log('Blur timeout completed');
    }, 500);
  }, []);

  // Handle keyboard hide
  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        // Don't close the dropdown immediately when keyboard hides
        // This allows time for touch events to complete
        console.log('Keyboard hidden');
      },
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);

  const insets = useSafeAreaInsets();

  // Show AboutUs page if requested
  if (showAboutUs) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#4285F4" />
        <View style={[styles.statusBarBackground, {height: insets.top}]} />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setShowAboutUs(false)}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
            <View style={styles.headerContent}>
              <Text style={styles.title}>About Us</Text>
            </View>
          </View>
          <AboutUsPage />
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#4285F4" />
      <View style={[styles.statusBarBackground, {height: insets.top}]} />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          {/* <TouchableOpacity
            style={styles.menuButton}
            onPress={() => setMenuVisible(true)}
          >
            <Text style={styles.menuButtonText}>☰</Text>
          </TouchableOpacity> */}
          <View style={styles.headerContent}>
            <Text style={styles.title}>Instant Email</Text>
            <Text style={styles.subtitle}>Compose and send emails quickly</Text>
          </View>
        </View>

        <View style={styles.emailComposerContainer}>
          <ScrollView
            style={styles.formContainer}
            ref={scrollViewRef}
            keyboardShouldPersistTaps="handled">
            <View style={styles.recipientRow}>
              <Text style={styles.fieldLabel}>To</Text>
              <TextInput
                ref={recipientInputRef}
                style={styles.recipientInput}
                placeholder="Recipients"
                placeholderTextColor="#adb5bd"
                keyboardType="email-address"
                autoCapitalize="none"
                value={to}
                onChangeText={setTo}
                onFocus={handleRecipientFocus}
                onBlur={handleRecipientBlur}
              />
            </View>

            <View style={styles.divider} />

            <View style={styles.subjectRow}>
              <Text style={styles.fieldLabel}>Subject</Text>
              <TextInput
                style={styles.subjectInput}
                placeholder="Subject"
                placeholderTextColor="#adb5bd"
                value={subject}
                onChangeText={setSubject}
              />
            </View>

            <View style={styles.divider} />

            <View style={styles.richEditorContainer}>
              <RichTextEditor
                ref={richTextEditorRef}
                initialContent=""
                placeholder="Compose email"
                onChange={setBody}
                height={250}
                onCursorPosition={handleCursorPosition}
              />
            </View>

            <AttachmentManager
              attachments={attachments}
              onRemoveAttachment={handleRemoveAttachment}
            />
            <TemplateBar
              onAddAttachment={handleAddAttachment}
              recentAttachments={recentAttachments}
              onSelectAttachment={handleSelectRecentAttachment}
              onClearRecentAttachments={handleClearRecentAttachments}
              templates={emailTemplates}
              onSelectTemplate={handleSelectTemplate}
              onCreateTemplate={handleOpenSaveTemplateModal}
              onRemoveTemplate={handleRemoveTemplate}
              onSend={handleSend}
            />

            <View style={styles.openMailAppContainer}>
              <TouchableOpacity
                style={styles.openMailAppButton}
                onPress={handleOpenApp}>
                <Icon name="mail" size={18} color="#4285F4" />
                <Text style={styles.openMailAppText}>Open Mail App</Text>
              </TouchableOpacity>
            </View>
            <AdComponent />
          </ScrollView>

          {/* Recent recipients dropdown */}
          <RecentRecipients
            recipients={recentRecipients}
            onSelectRecipient={handleSelectRecipient}
            onClearRecipients={handleClearRecipients}
            visible={recipientDropdownVisible}
            position={recipientInputPosition}
          />

          <SaveTemplateModal
            visible={saveTemplateModalVisible}
            onClose={() => setSaveTemplateModalVisible(false)}
            onSave={handleSaveTemplate}
            initialSubject={subject}
            initialBody={body}
          />

          {/* Dropdown Menu Modal */}
          <Modal
            animationType="fade"
            transparent={true}
            visible={menuVisible}
            onRequestClose={() => setMenuVisible(false)}>
            <TouchableOpacity
              style={styles.modalOverlay}
              activeOpacity={1}
              onPress={() => setMenuVisible(false)}>
              <View style={styles.menuContainer}>
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    Linking.openURL(
                      Platform.select({
                        ios: 'https://apps.apple.com/app/your-app-id',
                        android: 'market://details?id=com.instantemailcomposer',
                      }) || 'market://details?id=com.instantemailcomposer',
                    );
                    setMenuVisible(false);
                  }}>
                  <Text style={styles.menuItemText}>Rate App</Text>
                </TouchableOpacity>
                <View style={styles.menuDivider} />
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setShowAboutUs(true);
                    setMenuVisible(false);
                  }}>
                  <Text style={styles.menuItemText}>About Us</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </Modal>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#4285F4', // Match status bar color
  },
  statusBarBackground: {
    backgroundColor: '#4285F4',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 16,
    paddingTop:
      Platform.OS === 'android' ? 16 + (StatusBar.currentHeight || 0) : 16,
    backgroundColor: '#4285F4',
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  menuButton: {
    position: 'absolute',
    top: Platform.OS === 'android' ? 16 + (StatusBar.currentHeight || 0) : 16,
    right: 16,
    zIndex: 1,
    padding: 8,
  },
  menuButtonText: {
    fontSize: 20,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.9,
  },
  emailComposerContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
    backgroundColor: '#ffffff',
  },
  formContainer: {
    flex: 1,
    paddingBottom: 120, // Space for the compose toolbar and banner ad
  },
  recipientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#ffffff',
  },
  subjectRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#ffffff',
  },
  fieldLabel: {
    width: 60,
    fontSize: 16,
    color: '#4285F4', // Using Google blue color for consistency
    fontWeight: '500',
  },
  recipientInput: {
    flex: 1,
    fontSize: 16,
    color: '#202124',
    padding: 0,
  },
  subjectInput: {
    flex: 1,
    fontSize: 16,
    color: '#202124',
    padding: 0,
  },
  divider: {
    height: 1,
    backgroundColor: '#f1f3f5',
    marginHorizontal: 16,
  },
  body: {
    minHeight: 200,
    textAlignVertical: 'top',
    padding: 16,
    fontSize: 16,
    color: '#202124',
  },
  composeToolbar: {
    position: 'absolute',
    bottom: 60, // Adjusted to make room for the banner ad below
    left: 0,
    right: 0,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  openMailAppContainer: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 24,
  },
  openMailAppButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  openMailAppText: {
    color: '#4285F4',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  bannerContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    alignItems: 'center',
    paddingVertical: 8,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  richEditorContainer: {
    minHeight: 250,
    marginTop: 10,
    marginBottom: 20,
    marginHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop:
      Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 60 : 60,
    paddingRight: 16,
  },
  menuContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    minWidth: 150,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  menuItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  menuItemText: {
    fontSize: 16,
    color: '#202124',
    textAlign: 'center',
  },
  menuDivider: {
    height: 1,
    backgroundColor: '#e9ecef',
    marginHorizontal: 8,
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'android' ? 16 + (StatusBar.currentHeight || 0) : 16,
    left: 16,
    zIndex: 1,
    padding: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '600',
  },
});
